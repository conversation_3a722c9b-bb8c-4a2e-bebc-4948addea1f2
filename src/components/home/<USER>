'use client';

import { But<PERSON>, Translate } from '@/components/ui';
import { useFloatingFeedbackDialog } from '@/components/floating-ui';
import { motion } from 'framer-motion';
import { MessageSquare } from 'lucide-react';
import { useState } from 'react';
import { itemVariants } from './animation-variants';

export function FeedbackSection() {
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const feedbackDialog = useFloatingFeedbackDialog('feedback-dialog');

	const handleOpenDialog = () => {
		setIsDialogOpen(true);
		feedbackDialog.show();
	};

	const handleCloseDialog = () => {
		setIsDialogOpen(false);
		feedbackDialog.hide();
	};

	return (
		<div className="space-y-4">
			<h2 className="text-2xl font-bold text-center">
				<Translate text="feedback.title" />
			</h2>
			<motion.div
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.3 }}
				className="flex justify-center"
			>
				<motion.div variants={itemVariants}>
					<Button
						onClick={handleOpenDialog}
						className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300"
					>
						<MessageSquare className="h-4 w-4" />
						<Translate text="feedback.open_dialog" />
					</Button>
				</motion.div>
			</motion.div>
		</div>
	);
}
