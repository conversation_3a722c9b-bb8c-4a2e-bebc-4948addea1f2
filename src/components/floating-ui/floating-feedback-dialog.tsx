'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Translate, useToast } from '@/components/ui';
import { FEEDBACK_SECTION_LOADING_KEYS } from '@/constants/loading-keys';
import { LOADING_SCOPES } from '@/constants/loading-scopes';
import { useScopedLoading, useTranslation } from '@/contexts';
import { useFloatingModal } from '@/hooks/use-floating-ui';
import { motion } from 'framer-motion';
import { Send, X } from 'lucide-react';
import { FormEvent, useState } from 'react';

export function useFloatingFeedbackDialog(id = 'floating-feedback-dialog') {
	const [feedbackMessage, setFeedbackMessage] = useState('');
	const { setLoading: setSubmittingLoading, getLoading: getSubmittingLoading } = useScopedLoading(
		LOADING_SCOPES.FEEDBACK_SECTION
	);
	const { t } = useTranslation();
	const { toast } = useToast();

	const handleFeedbackSubmit = async (e: FormEvent) => {
		e.preventDefault();

		// Validate required fields
		if (!feedbackMessage) {
			toast({
				title: t('toast.missing_info'),
				description: t('toast.missing_info_desc'),
			});
			return;
		}

		setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, true);

		try {
			const response = await fetch('/api/feedback', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ message: feedbackMessage }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to submit feedback');
			}

			toast({
				title: t('toast.feedback_sent'),
				description: t('toast.feedback_sent_desc'),
			});
			setFeedbackMessage('');
			hide();
		} catch (error) {
			toast({
				variant: 'destructive',
				title: t('toast.submission_failed'),
				description:
					error instanceof Error ? error.message : t('toast.submission_failed_desc'),
			});
		} finally {
			setSubmittingLoading(FEEDBACK_SECTION_LOADING_KEYS.SUBMIT_FEEDBACK, false);
		}
	};

	const dialogContent = (
		<div className="bg-background border rounded-lg shadow-lg p-6 w-full max-w-md mx-auto">
			{/* Header */}
			<div className="flex items-center justify-between mb-4">
				<h2 className="text-xl font-semibold">
					<Translate text="feedback.title" />
				</h2>
				<Button
					variant="ghost"
					size="sm"
					onClick={() => hide()}
					className="h-8 w-8 p-0 hover:bg-muted"
				>
					<X className="h-4 w-4" />
				</Button>
			</div>

			{/* Form */}
			<motion.form
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.3 }}
				className="space-y-4"
				onSubmit={handleFeedbackSubmit}
			>
				<div>
					<label htmlFor="feedback" className="block text-sm font-medium mb-2">
						<Translate text="feedback.message" />
					</label>
					<Textarea
						id="feedback"
						value={feedbackMessage}
						onChange={(e) => setFeedbackMessage(e.target.value)}
						className="w-full min-h-[120px] focus:ring-2 focus:ring-primary/20 transition-all duration-200"
						placeholder={t('feedback.placeholder.message')}
						required
					/>
				</div>
				<div className="flex justify-end gap-2">
					<Button
						type="button"
						variant="outline"
						onClick={() => hide()}
						disabled={getSubmittingLoading('submitFeedback')}
					>
						<Translate text="common.cancel" />
					</Button>
					<Button
						type="submit"
						disabled={getSubmittingLoading('submitFeedback')}
						className="flex items-center gap-2"
					>
						{getSubmittingLoading('submitFeedback') ? (
							<LoadingSpinner size="sm" />
						) : (
							<Send className="h-4 w-4" />
						)}
						<Translate text="feedback.submit" />
					</Button>
				</div>
			</motion.form>
		</div>
	);

	const { show, hide, toggle, isVisible } = useFloatingModal(id, dialogContent, {
		priority: 'high',
		persistent: true,
		style: {
			backgroundColor: 'rgba(0, 0, 0, 0.5)',
			backdropFilter: 'blur(4px)',
		},
	});

	return {
		show,
		hide,
		toggle,
		isVisible,
	};
}
