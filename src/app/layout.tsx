import { DOMClientSettings } from '@/app/components/dom-client-settings';
import '@/app/globals.css';
import { ProgressBar, ThemeProvider } from '@/components/ui';
import { FloatingUIManager, FloatingUIProvider } from '@/components/floating-ui';
import { AuthProvider } from '@/contexts/auth-context';
import { LoadingProvider } from '@/contexts/loading-context';
import { SimpleFloatingManager, SimpleFloatingProvider } from '@/contexts/simple-floating-context';
import { ToastProvider } from '@/contexts/toast-context';
import { TranslationProvider } from '@/contexts/translation-context';
import { ErrorManagementProvider } from '@/providers/error-management-provider';
import { Metadata } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mon<PERSON> } from 'next/font/google';
import { Toaster } from 'sonner';

const geist = Geist({
	subsets: ['latin'],
	variable: '--font-geist',
	preload: true,
});

const geistMono = Geist_Mono({
	subsets: ['latin'],
	variable: '--font-geist-mono',
	preload: true,
});

export const metadata: Metadata = {
	title: 'Vocab',
	description: 'Learn new vocabulary with AI assistance',
};

export default async function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<meta
					name="viewport"
					content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
				/>
				<link rel="manifest" href="/manifest.json" />
			</head>
			<body
				className={`${geist.variable} ${geistMono.variable} min-h-screen flex flex-col`}
				role="application"
				aria-label="Vocab Learning Application"
				aria-live="polite"
				suppressHydrationWarning
			>
				<ErrorManagementProvider
					config={{
						enableErrorReporting: true,
						enableNetworkDetection: true,
						enableApiInterception: true,
						environment: process.env.NODE_ENV,
						buildVersion: process.env.NEXT_PUBLIC_BUILD_VERSION,
					}}
				>
					<ThemeProvider>
						<TranslationProvider>
							<LoadingProvider>
								<ToastProvider>
									<SimpleFloatingProvider>
										<FloatingUIProvider>
											<AuthProvider>
												<main className="flex-grow px-2 sm:px-6 lg:px-9 py-2 sm:py-4 lg:py-8">
													<div className="container mx-auto max-w-7xl">
														<div className="w-full">{children}</div>
													</div>
												</main>
												<ProgressBar />
												<DOMClientSettings />
												<SimpleFloatingManager />
												<FloatingUIManager />
											</AuthProvider>
										</FloatingUIProvider>
									</SimpleFloatingProvider>
								</ToastProvider>
							</LoadingProvider>
						</TranslationProvider>
					</ThemeProvider>
					<Toaster
						position="top-right"
						richColors
						closeButton
						expand={false}
						visibleToasts={5}
						toastOptions={{
							style: {
								background: 'hsl(var(--background))',
								color: 'hsl(var(--foreground))',
								border: '1px solid hsl(var(--border))',
							},
							className: 'sonner-toast',
						}}
					/>
				</ErrorManagementProvider>
			</body>
		</html>
	);
}
