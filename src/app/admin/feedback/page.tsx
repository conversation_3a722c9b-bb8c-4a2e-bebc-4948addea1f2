'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	Input,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Textarea,
	useToast,
} from '@/components/ui';
import { Filter, RefreshCw, Search } from 'lucide-react';
import { useState, useEffect } from 'react';

interface User {
	id: string;
	username: string | null;
	provider: string;
	provider_id: string;
	created_at: string;
}

interface Feedback {
	id: string;
	message: string;
	user_id: string;
	user: User;
	status: string;
	created_at: string;
}

export default function FeedbackDashboard() {
	const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
	const [loading, setLoading] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const [statusFilter, setStatusFilter] = useState('all');
	const { toast } = useToast();

	const loadFeedbacks = async () => {
		setLoading(true);
		try {
			const response = await fetch('/api/admin/feedback');

			if (response.ok) {
				const data = await response.json();
				setFeedbacks(data);
			} else {
				toast({
					variant: 'destructive',
					title: 'Error',
					description: 'Failed to fetch feedbacks',
				});
			}
		} catch (error) {
			toast({
				variant: 'destructive',
				title: 'Error',
				description: 'Failed to load feedbacks',
			});
		} finally {
			setLoading(false);
		}
	};

	const refreshFeedbacks = async () => {
		await loadFeedbacks();
	};

	const updateFeedbackStatus = async (id: string, status: string) => {
		try {
			const response = await fetch('/api/admin/feedback', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ id, status }),
			});

			if (response.ok) {
				setFeedbacks((prev) =>
					prev.map((feedback) =>
						feedback.id === id ? { ...feedback, status } : feedback
					)
				);
				toast({
					title: 'Success',
					description: 'Feedback status updated',
				});
			} else {
				toast({
					variant: 'destructive',
					title: 'Error',
					description: 'Failed to update status',
				});
			}
		} catch (error) {
			toast({
				variant: 'destructive',
				title: 'Error',
				description: 'Failed to update status',
			});
		}
	};

	const filteredFeedbacks = feedbacks.filter((feedback) => {
		const matchesSearch =
			feedback.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
			feedback.user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
			feedback.user.provider_id.toLowerCase().includes(searchTerm.toLowerCase());

		const matchesStatus = statusFilter === 'all' || feedback.status === statusFilter;

		return matchesSearch && matchesStatus;
	});

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'pending':
				return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
			case 'reviewed':
				return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
			case 'resolved':
				return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
			case 'dismissed':
				return 'bg-gray-100 text-gray-800 dark:bg-gray-800/50 dark:text-gray-300';
			default:
				return 'bg-gray-100 text-gray-800 dark:bg-gray-800/50 dark:text-gray-300';
		}
	};

	// Load feedbacks on component mount
	useEffect(() => {
		loadFeedbacks();
	}, []);

	return (
		<div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
			<div className="max-w-7xl mx-auto">
				<div className="flex justify-between items-center mb-6">
					<h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
						Feedback Dashboard
					</h1>
					<Button onClick={refreshFeedbacks} disabled={loading}>
						<RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
						Refresh
					</Button>
				</div>

				{/* Filters */}
				<Card className="mb-6">
					<CardContent className="pt-6">
						<div className="flex gap-4 items-center">
							<div className="relative flex-1">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
								<Input
									placeholder="Search feedbacks..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									className="pl-10"
								/>
							</div>
							<div className="flex items-center gap-2">
								<Filter className="h-4 w-4 text-gray-400 dark:text-gray-500" />
								<Select value={statusFilter} onValueChange={setStatusFilter}>
									<SelectTrigger className="w-40">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="all">All Status</SelectItem>
										<SelectItem value="pending">Pending</SelectItem>
										<SelectItem value="reviewed">Reviewed</SelectItem>
										<SelectItem value="resolved">Resolved</SelectItem>
										<SelectItem value="dismissed">Dismissed</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Stats */}
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
					<Card>
						<CardContent className="pt-6">
							<div className="text-2xl font-bold">{feedbacks.length}</div>
							<p className="text-sm text-gray-600 dark:text-gray-400">
								Total Feedbacks
							</p>
						</CardContent>
					</Card>
					<Card>
						<CardContent className="pt-6">
							<div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
								{feedbacks.filter((f) => f.status === 'pending').length}
							</div>
							<p className="text-sm text-gray-600 dark:text-gray-400">Pending</p>
						</CardContent>
					</Card>
					<Card>
						<CardContent className="pt-6">
							<div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
								{feedbacks.filter((f) => f.status === 'reviewed').length}
							</div>
							<p className="text-sm text-gray-600 dark:text-gray-400">Reviewed</p>
						</CardContent>
					</Card>
					<Card>
						<CardContent className="pt-6">
							<div className="text-2xl font-bold text-green-600 dark:text-green-400">
								{feedbacks.filter((f) => f.status === 'resolved').length}
							</div>
							<p className="text-sm text-gray-600 dark:text-gray-400">Resolved</p>
						</CardContent>
					</Card>
				</div>

				{/* Feedback List */}
				<div className="space-y-4">
					{filteredFeedbacks.map((feedback) => (
						<Card key={feedback.id}>
							<CardContent className="pt-6">
								<div className="flex justify-between items-start mb-4">
									<div className="flex-1">
										<div className="flex items-center gap-2 mb-2">
											<Badge className={getStatusColor(feedback.status)}>
												{feedback.status}
											</Badge>
											<span className="text-sm text-gray-500 dark:text-gray-400">
												{new Date(feedback.created_at).toLocaleString()}
											</span>
										</div>
										<div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
											<strong>User:</strong>{' '}
											{feedback.user.username || 'Anonymous'}(
											{feedback.user.provider}: {feedback.user.provider_id})
										</div>
									</div>
									<Select
										value={feedback.status}
										onValueChange={(status) =>
											updateFeedbackStatus(feedback.id, status)
										}
									>
										<SelectTrigger className="w-32">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="pending">Pending</SelectItem>
											<SelectItem value="reviewed">Reviewed</SelectItem>
											<SelectItem value="resolved">Resolved</SelectItem>
											<SelectItem value="dismissed">Dismissed</SelectItem>
										</SelectContent>
									</Select>
								</div>
								<Textarea
									value={feedback.message}
									readOnly
									className="min-h-[100px] bg-gray-50"
								/>
							</CardContent>
						</Card>
					))}
				</div>

				{filteredFeedbacks.length === 0 && (
					<Card>
						<CardContent className="pt-6 text-center text-gray-500">
							No feedbacks found matching your criteria.
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
